<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Announcement;

class HomeController extends Controller
{
    public function index()
    {
        // Get latest courses with pre-order and start course logic
        $featuredCourses = Course::where('status', 'published')
            ->with('teacher')
            ->latest()
            ->take(6)
            ->get();

        // Get featured announcements for info section
        $announcements = Announcement::published()
            ->featured()
            ->latest()
            ->take(5)
            ->get();

        // Get all announcements for general display
        $allAnnouncements = Announcement::published()
            ->latest()
            ->take(10)
            ->get();

        // Sample product data (in real app, this would come from a Product model)
        $featuredProduct = (object) [
            'title' => 'Antologi Cerpen Edunetra',
            'description' => 'Kumpulan cerita pendek inspiratif dari komunitas Edunetra yang mengangkat tema inklusi, pendidikan, dan pemberdayaan difabel netra.',
            'price' => 75000,
            'category' => 'Sastra & Edukasi',
            'estimated_release' => 'Desember 2024',
            'is_preorder' => true,
            'image' => null
        ];

        return view('home', compact('featuredCourses', 'announcements', 'allAnnouncements', 'featuredProduct'));
    }

    public function courses(Request $request)
    {
        $query = Course::where('status', 'published')->with('teacher');

        // Add search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereHas('teacher', function($teacherQuery) use ($searchTerm) {
                      $teacherQuery->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Add level filter
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Add sorting
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'title':
                $query->orderBy('title', 'asc');
                break;
            default:
                $query->latest();
                break;
        }

        $courses = $query->paginate(12)->withQueryString();

        return view('courses.index', compact('courses'));
    }

    public function about()
    {
        return view('about');
    }

    public function contact()
    {
        return view('contact');
    }
}
