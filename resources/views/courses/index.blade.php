@extends('layouts.app')

@section('title', 'All Courses - Edunetra')

@section('content')
<!-- <PERSON> Header -->
<section class="bg-edunetra-dark text-white py-5">
    <div class="container text-center">
        <h1 class="display-4 fw-bold mb-3">All Courses</h1>
        <p class="fs-5 opacity-75">Explore our comprehensive learning programs</p>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row g-3 align-items-center">
            <!-- Search -->
            <div class="col-lg-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Search courses...">
                    <button class="btn btn-edunetra" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="col-lg-6">
                <div class="row g-2">
                    <div class="col-md-6">
                        <select class="form-select">
                            <option value="">All Levels</option>
                            <option value="beginner">Beginner</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="advanced">Advanced</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select">
                            <option value="">All Categories</option>
                            <option value="english">English</option>
                            <option value="programming">Programming</option>
                            <option value="design">Design</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-5">
    <div class="container">
        @if($courses->count() > 0)
            <div class="row g-4">
                @foreach($courses as $course)
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <div class="card h-100 border-0 shadow">
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 180px;">
                                @if($course->image)
                                    <img src="{{ $course->image }}" alt="{{ $course->title }}" class="img-fluid">
                                @else
                                    <div class="text-center text-muted">
                                        <i class="bi bi-book fs-1 mb-2"></i>
                                        <p class="mb-0 small">{{ Str::limit($course->title, 20) }}</p>
                                    </div>
                                @endif
                            </div>

                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-edunetra-yellow text-dark">
                                        {{ ucfirst($course->level) }}
                                    </span>
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> {{ $course->duration_weeks }} weeks
                                    </small>
                                </div>

                                <h6 class="card-title fw-bold">{{ $course->title }}</h6>
                                <p class="card-text text-muted small flex-grow-1">{{ Str::limit($course->description, 80) }}</p>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-person"></i> {{ $course->teacher->name }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="bi bi-people"></i> {{ $course->getEnrolledStudentsCount() }}
                                        @if($course->max_students)
                                            /{{ $course->max_students }}
                                        @endif
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold">
                                        @if($course->price > 0)
                                            Rp {{ number_format($course->price, 0, ',', '.') }}
                                        @else
                                            <span class="text-success">Free</span>
                                        @endif
                                    </span>
                                    <a href="{{ route('courses.show', $course->slug) }}"
                                       class="btn btn-edunetra btn-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="row mt-5">
                <div class="col-12 d-flex justify-content-center">
                    {{ $courses->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="bi bi-book display-1 text-muted mb-3"></i>
                <h3 class="fw-bold mb-2">No courses found</h3>
                <p class="text-muted">Try adjusting your search or filter criteria.</p>
            </div>
        @endif
    </div>
</section>
@endsection
